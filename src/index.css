@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.google.com/share?selection.family=<PERSON>in+Sans|Michroma|Oswald:wght@200..700|Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Line clamp utilities */
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/
.logo {
    font-family: '<PERSON>', sans-serif;
    font-size: 48px; /* adjust as needed */
    font-weight: 500; /* adjust if you want it bolder */
  }

  
@layer base {
  :root {
    /* Light mode variables */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 241 95% 60%;
    --primary-foreground: 210 40% 98%;
    --primary-glow: 241 95% 60%;

    --accent-red: 0 84% 60%;
    --accent-red-foreground: 210 40% 98%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;

    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --success: 142 76% 36%;
    --success-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 241 95% 60%;

    --radius: 0.75rem;

    /* Custom design tokens for Cable Network Solutions - Light Mode */
    --hero-gradient: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(210 40% 98%) 50%, hsl(var(--background)) 100%);
    --primary-gradient: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary-glow)) 100%);
    --accent-gradient: linear-gradient(135deg, hsl(var(--accent-red)) 0%, hsl(var(--primary)) 100%);
    --subtle-gradient: linear-gradient(135deg, hsl(var(--primary) / 0.05) 0%, hsl(var(--accent-red) / 0.05) 100%);
    --text-gradient: linear-gradient(135deg, hsl(var(--accent-red)) 0%, hsl(var(--primary)) 70%);
    --card-gradient: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(210 40% 98%) 100%);
    --glow-shadow: 0 0 50px hsl(var(--primary) / 0.15);
    --elegant-shadow: 0 10px 30px -10px hsl(var(--primary) / 0.1);
    --card-shadow: 0 4px 20px hsl(210 40% 96% / 0.8);
    --accent-glow: 0 0 30px hsl(var(--accent-red) / 0.2);

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.9% 10%;
    --sidebar-primary: 241 95% 60%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 220 14.3% 95.9%;
    --sidebar-accent-foreground: 220 8.9% 46.1%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 241 95% 60%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 225 100% 3.9%;
    --card-foreground: 210 40% 98%;

    --popover: 225 100% 3.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 241 95% 60%;
    --primary-foreground: 210 40% 98%;
    --primary-glow: 241 95% 60%;

    --accent-red: 0 84% 60%;
    --accent-red-foreground: 210 40% 98%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 224.3 76.3% 48%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --success: 142 76% 36%;
    --success-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 241 95% 60%;

    /* Custom design tokens for Cable Network Solutions - Dark Mode */
    --hero-gradient: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(217.2 32.6% 8.5%) 50%, hsl(var(--background)) 100%);
    --primary-gradient: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary-glow)) 100%);
    --accent-gradient: linear-gradient(135deg, hsl(var(--accent-red)) 0%, hsl(var(--primary)) 100%);
    --subtle-gradient: linear-gradient(135deg, hsl(var(--primary) / 0.1) 0%, hsl(var(--accent-red) / 0.1) 100%);
    --text-gradient: linear-gradient(135deg, hsl(var(--accent-red)) 0%, hsl(var(--primary)) 70%);
    --card-gradient: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(217.2 32.6% 12.5%) 100%);
    --glow-shadow: 0 0 50px hsl(var(--primary) / 0.3);
    --elegant-shadow: 0 10px 30px -10px hsl(var(--primary) / 0.2);
    --card-shadow: 0 4px 20px hsl(217.2 32.6% 2.5% / 0.5);
    --accent-glow: 0 0 30px hsl(var(--accent-red) / 0.3);

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 241 95% 60%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 241 95% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* Remove forced dark mode - let next-themes handle it */
}

@layer components {
  .hero-gradient {
    background: var(--hero-gradient);
  }

  .primary-gradient {
    background: var(--primary-gradient);
  }

  .accent-gradient {
    background: var(--accent-gradient);
  }

  .subtle-gradient {
    background: var(--subtle-gradient);
  }

  .text-gradient {
    background: var(--text-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .card-gradient {
    background: var(--card-gradient);
  }

  .glow-shadow {
    box-shadow: var(--glow-shadow);
  }

  .elegant-shadow {
    box-shadow: var(--elegant-shadow);
  }

  .card-shadow {
    box-shadow: var(--card-shadow);
  }

  .accent-glow {
    box-shadow: var(--accent-glow);
  }

  /* Modern responsive grid utilities */
  .grid-responsive {
    display: grid;
    gap: 1rem;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }

  .grid-responsive-sm {
    display: grid;
    gap: 0.75rem;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }

  .grid-responsive-lg {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  }

  /* Container utilities for better responsive design */
  .container-responsive {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  @media (min-width: 640px) {
    .container-responsive {
      padding: 0 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .container-responsive {
      padding: 0 2rem;
    }
  }

  /* Touch-friendly button sizing */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Smooth transitions for theme switching */
  .theme-transition {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
  }
}