import { Shopping<PERSON><PERSON>, Star } from 'lucide-react';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useCartStore } from '@/store/cartStore';
import { Product } from '@/types/product';
import { useToast } from '@/hooks/use-toast';
import { formatProductName, formatProductDescription } from '@/lib/productFormatting';

interface ProductCardProps {
  product: Product;
}

export const ProductCard = ({ product }: ProductCardProps) => {
  const { addItem } = useCartStore();
  const { toast } = useToast();

  const handleAddToCart = () => {
    addItem(product, 1);
    toast({
      title: "Added to cart",
      description: `${formatProductName(product.name)} has been added to your cart.`,
      variant: "success",
    });
  };

  return (
    <Card className="group overflow-hidden hover:card-shadow transition-all duration-300 hover:scale-[1.02] card-gradient border-border/50 hover:border-primary/20 flex flex-col h-full">
      <div className="aspect-video overflow-hidden">
        <img
          src={product.imageUrl}
          alt={formatProductName(product.name)}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
        />
      </div>
      
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-2">
          <h3 className="font-semibold text-lg leading-tight group-hover:text-primary transition-colors line-clamp-2 break-words min-h-[3rem]">
            {formatProductName(product.name)}
          </h3>
          {product.featured && (
            <Badge variant="secondary" className="ml-2 flex-shrink-0">
              <Star className="w-3 h-3 mr-1" />
              Featured
            </Badge>
          )}
        </div>

        <p className="text-muted-foreground text-sm mb-3 line-clamp-2">
          {formatProductDescription(product.description)}
        </p>
        
        <div className="flex items-center justify-between">
          <div className="text-2xl font-bold text-foreground group-hover:text-primary transition-colors">
            ${product.price.toFixed(2)}
          </div>
          {product.inStock ? (
            <Badge variant="success">
              In Stock
            </Badge>
          ) : (
            <Badge variant="destructive">
              Out of Stock
            </Badge>
          )}
        </div>
      </CardContent>
      
      <CardFooter className="p-4 pt-0 mt-auto">
        <Button
          className="w-full"
          variant="default"
          disabled={!product.inStock}
          onClick={handleAddToCart}
        >
          <ShoppingCart className="w-4 h-4 mr-2" />
          Add to Cart
        </Button>
      </CardFooter>
    </Card>
  );
};