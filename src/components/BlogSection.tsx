import React from 'react';
import { cn } from '@/lib/utils';

interface BlogSectionProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'highlight' | 'callout';
}

const BlogSection: React.FC<BlogSectionProps> = ({ 
  children, 
  className,
  variant = 'default' 
}) => {
  const baseClasses = "mb-8 p-6 rounded-xl";
  
  const variantClasses = {
    default: "bg-transparent",
    highlight: "bg-secondary/20 border border-border/30",
    callout: "bg-primary/5 border-l-4 border-primary/30 pl-8"
  };

  return (
    <div className={cn(baseClasses, variantClasses[variant], className)}>
      {children}
    </div>
  );
};

export default BlogSection;
