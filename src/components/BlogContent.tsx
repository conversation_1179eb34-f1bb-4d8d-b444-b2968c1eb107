import React from 'react';
import ReactMarkdown from 'react-markdown';
import { Badge } from '@/components/ui/badge';
import { Tag } from 'lucide-react';

interface BlogContentProps {
  content: string;
  tags: string[];
}

const BlogContent: React.FC<BlogContentProps> = ({ content, tags }) => {
  // Custom components for markdown rendering
  const components = {
    h1: ({ children }: any) => (
      <h1 className="text-4xl font-bold mb-8 mt-12 leading-tight text-foreground border-b-2 border-primary/20 pb-4">
        {children}
      </h1>
    ),
    h2: ({ children }: any) => (
      <h2 className="text-3xl font-bold mb-6 mt-10 leading-tight text-foreground border-b border-border/30 pb-3">
        {children}
      </h2>
    ),
    h3: ({ children }: any) => (
      <h3 className="text-2xl font-semibold mb-4 mt-8 leading-tight text-primary">
        {children}
      </h3>
    ),
    h4: ({ children }: any) => (
      <h4 className="text-xl font-semibold mb-3 mt-6 leading-tight text-foreground">
        {children}
      </h4>
    ),
    h5: ({ children }: any) => (
      <h5 className="text-lg font-semibold mb-2 mt-4 leading-tight text-foreground">
        {children}
      </h5>
    ),
    h6: ({ children }: any) => (
      <h6 className="text-base font-semibold mb-2 mt-4 leading-tight text-muted-foreground">
        {children}
      </h6>
    ),
    p: ({ children }: any) => (
      <p className="text-muted-foreground leading-relaxed mb-6 text-lg">
        {children}
      </p>
    ),
    ul: ({ children }: any) => (
      <ul className="text-muted-foreground space-y-3 mb-6 ml-6 list-disc">
        {children}
      </ul>
    ),
    ol: ({ children }: any) => (
      <ol className="text-muted-foreground space-y-3 mb-6 ml-6 list-decimal">
        {children}
      </ol>
    ),
    li: ({ children }: any) => (
      <li className="leading-relaxed text-lg">
        {children}
      </li>
    ),
    blockquote: ({ children }: any) => (
      <blockquote className="border-l-4 border-primary/30 bg-secondary/20 p-6 rounded-r-lg mb-6 italic">
        <div className="text-muted-foreground">
          {children}
        </div>
      </blockquote>
    ),
    code: ({ children, className }: any) => {
      const isInline = !className;
      if (isInline) {
        return (
          <code className="bg-secondary/50 px-2 py-1 rounded text-sm font-mono text-foreground">
            {children}
          </code>
        );
      }
      return (
        <pre className="bg-secondary/30 border border-border/50 rounded-xl p-4 mb-6 overflow-x-auto">
          <code className="text-sm font-mono text-foreground">
            {children}
          </code>
        </pre>
      );
    },
    pre: ({ children }: any) => (
      <div className="bg-secondary/30 border border-border/50 rounded-xl p-4 mb-6 overflow-x-auto">
        {children}
      </div>
    ),
    a: ({ children, href }: any) => (
      <a 
        href={href} 
        className="text-primary no-underline hover:underline font-medium transition-colors"
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
    strong: ({ children }: any) => (
      <strong className="text-foreground font-semibold">
        {children}
      </strong>
    ),
    em: ({ children }: any) => (
      <em className="text-foreground italic">
        {children}
      </em>
    ),
    hr: () => (
      <hr className="border-border/30 my-12" />
    ),
    table: ({ children }: any) => (
      <div className="overflow-x-auto mb-6">
        <table className="w-full border-collapse border border-border/30 rounded-lg">
          {children}
        </table>
      </div>
    ),
    th: ({ children }: any) => (
      <th className="border border-border/30 bg-secondary/20 px-4 py-3 text-left font-semibold text-foreground">
        {children}
      </th>
    ),
    td: ({ children }: any) => (
      <td className="border border-border/30 px-4 py-3 text-muted-foreground">
        {children}
      </td>
    ),
  };

  return (
    <div className="max-w-none">
      <div className="prose-custom">
        <ReactMarkdown components={components}>
          {content}
        </ReactMarkdown>
      </div>
      
      {/* Tags Section */}
      <div className="mt-16 pt-8 border-t border-border/30">
        <div className="flex items-center gap-3 flex-wrap">
          <div className="flex items-center gap-2 text-muted-foreground">
            <Tag className="h-5 w-5" />
            <span className="text-sm font-semibold">Tags:</span>
          </div>
          <div className="flex gap-2 flex-wrap">
            {tags.map((tag) => (
              <Badge 
                key={tag} 
                variant="outline" 
                className="text-sm px-3 py-1 hover:bg-primary hover:text-primary-foreground transition-colors cursor-pointer"
              >
                {tag}
              </Badge>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlogContent;
