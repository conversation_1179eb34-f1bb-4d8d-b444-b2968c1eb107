import { Link } from 'react-router-dom';
import { Mail, Phone, MapPin } from 'lucide-react';

export const Footer = () => {
  return (
    <footer className="bg-card border-t border-border theme-transition">
      <div className="container-responsive py-8 sm:py-12">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div>
              <div className="text-lg font-bold text-primary">Cable Network Solutions</div>
            </div>
            <p className="text-muted-foreground text-sm">
              Leading provider of telecommunications equipment and solutions.
              Connecting businesses with reliable network infrastructure.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-semibold text-foreground mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="text-muted-foreground hover:text-primary transition-colors text-sm">
                  Home
                </Link>
              </li>
              <li>
                <Link to="/about" className="text-muted-foreground hover:text-primary transition-colors text-sm">
                  About Us
                </Link>
              </li>
              <li>
                <Link to="/contact" className="text-muted-foreground hover:text-primary transition-colors text-sm">
                  Contact
                </Link>
              </li>
              <li>
                <Link to="/cart" className="text-muted-foreground hover:text-primary transition-colors text-sm">
                  Shopping Cart
                </Link>
              </li>
            </ul>
          </div>

          {/* Products */}
          <div>
            <h3 className="font-semibold text-foreground mb-4">Products</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/products/fibre" className="text-muted-foreground hover:text-primary transition-colors text-sm">
                  Fibre Optic Solutions
                </Link>
              </li>
              <li>
                <Link to="/products/lan" className="text-muted-foreground hover:text-primary transition-colors text-sm">
                  LAN Equipment
                </Link>
              </li>
              <li>
                <Link to="/products/switches" className="text-muted-foreground hover:text-primary transition-colors text-sm">
                  Network Switches
                </Link>
              </li>
              <li>
                <Link to="/products/radios" className="text-muted-foreground hover:text-primary transition-colors text-sm">
                  Radio Equipment
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="font-semibold text-foreground mb-4">Contact</h3>
            <ul className="space-y-3">
              <li className="flex items-center space-x-3">
                <Mail className="h-4 w-4 text-primary" />
                <span className="text-muted-foreground text-sm"><EMAIL></span>
              </li>
              <li className="flex items-center space-x-3">
                <Phone className="h-4 w-4 text-primary" />
                <span className="text-muted-foreground text-sm">(+260) 955-847117</span>
              </li>
              <li className="flex items-start space-x-3">
                <MapPin className="h-4 w-4 text-primary mt-0.5" />
                <span className="text-muted-foreground text-sm">
                  Garden View Offices<br />
                  Block B6 North, Lilayi, Lusaka
                </span>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-8 pt-8 border-t border-border flex flex-col md:flex-row justify-between items-center">
          <div className="text-muted-foreground text-sm">
            © 2025 Cable Network Solutions. All rights reserved.
          </div>
          
          <div className="flex space-x-4 mt-4 md:mt-0">
            <a href="https://www.facebook.com/share/1763CXaYgi/?mibextid=wwXIfr" className="text-muted-foreground hover:text-primary transition-colors touch-target p-2 -m-2 rounded-md hover:bg-accent/50">
              <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
              </svg>
            </a>
            <a href="https://www.instagram.com/cablenetworksolutions?igsh=NXpubWY1bDh0YzJ4" className="text-muted-foreground hover:text-primary transition-colors touch-target p-2 -m-2 rounded-md hover:bg-accent/50">
              <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path fillRule="evenodd" d="M12.017 0C8.396 0 7.989.013 7.041.048 6.094.082 5.52.204 5.012.388a6.5 6.5 0 00-2.346 1.267A6.5 6.5 0 00.388 5.012C.204 5.52.082 6.094.048 7.041.013 7.989 0 8.396 0 12.017c0 3.624.013 4.09.048 5.016.034.947.156 1.521.34 2.029a6.5 6.5 0 001.267 2.346 6.5 6.5 0 002.346 1.267c.508.184 1.082.306 2.029.34.926.035 1.392.048 5.016.048 3.624 0 4.09-.013 5.016-.048.947-.034 1.521-.156 2.029-.34a6.5 6.5 0 002.346-1.267 6.5 6.5 0 001.267-2.346c.184-.508.306-1.082.34-2.029.035-.926.048-1.392.048-5.016 0-3.624-.013-4.09-.048-5.016-.034-.947-.156-1.521-.34-2.029a6.5 6.5 0 00-1.267-2.346A6.5 6.5 0 0019.029.388c-.508-.184-1.082-.306-2.029-.34C16.074.013 15.668 0 12.017 0zM12.017 2.13c3.568 0 3.993.014 5.404.048.823.034 1.269.156 1.565.26.393.152.674.334.969.63.295.295.478.576.63.969.104.296.226.742.26 1.565.034 1.411.048 1.836.048 5.404 0 3.568-.014 3.993-.048 5.404-.034.823-.156 1.269-.26 1.565a2.607 2.607 0 01-.63.969 2.607 2.607 0 01-.969.63c-.296.104-.742.226-1.565.26-1.411.034-1.836.048-5.404.048-3.568 0-3.993-.014-5.404-.048-.823-.034-1.269-.156-1.565-.26a2.607 2.607 0 01-.969-.63 2.607 2.607 0 01-.63-.969c-.104-.296-.226-.742-.26-1.565-.034-1.411-.048-1.836-.048-5.404 0-3.568.014-3.993.048-5.404.034-.823.156-1.269.26-1.565.152-.393.334-.674.63-.969.295-.295.576-.478.969-.63.296-.104.742-.226 1.565-.26 1.411-.034 1.836-.048 5.404-.048z" clipRule="evenodd" />
                <path fillRule="evenodd" d="M12.017 5.838a6.179 6.179 0 100 12.358 6.179 6.179 0 000-12.358zM12.017 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z" clipRule="evenodd" />
              </svg>
            </a>
            <a href="https://www.linkedin.com/company/cable-network-solutionszm/" className="text-muted-foreground hover:text-primary transition-colors touch-target p-2 -m-2 rounded-md hover:bg-accent/50">
              <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path fillRule="evenodd" d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" clipRule="evenodd" />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};