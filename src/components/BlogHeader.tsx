import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, Clock, Share2, Star } from 'lucide-react';
import { BlogPost } from '@/lib/blogData';

interface BlogHeaderProps {
  post: BlogPost;
  onShare: () => void;
}

const BlogHeader: React.FC<BlogHeaderProps> = ({ post, onShare }) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <section className="py-16 lg:py-20">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-3 mb-6">
            <Badge variant="secondary" className="px-3 py-1 text-sm font-medium">
              {post.category}
            </Badge>
            {post.featured && (
              <Badge className="bg-primary text-primary-foreground border-primary px-3 py-1 text-sm font-medium">
                <Star className="h-3 w-3 mr-1 fill-current" />
                Featured
              </Badge>
            )}
          </div>

          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-8 leading-tight bg-gradient-to-br from-foreground to-foreground/70 bg-clip-text text-transparent">
            {post.title}
          </h1>

          <p className="text-xl lg:text-2xl text-muted-foreground mb-10 max-w-4xl mx-auto leading-relaxed">
            {post.excerpt}
          </p>

          <div className="flex items-center justify-center gap-8 text-sm text-muted-foreground mb-10">
            <div className="flex items-center gap-2 bg-secondary/30 px-4 py-2 rounded-full">
              <Calendar className="h-4 w-4" />
              <span className="font-medium">{formatDate(post.date)}</span>
            </div>
            <div className="flex items-center gap-2 bg-secondary/30 px-4 py-2 rounded-full">
              <Clock className="h-4 w-4" />
              <span className="font-medium">{post.readTime}</span>
            </div>
          </div>

          <Button
            onClick={onShare}
            variant="outline"
            className="group border-2 hover:bg-primary hover:text-primary-foreground transition-all duration-300"
          >
            <Share2 className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform" />
            Share Article
          </Button>
        </div>
      </div>

      {/* Featured Image - Wider container */}
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="aspect-video overflow-hidden rounded-3xl shadow-2xl mb-2 ring-1 ring-border/50">
          <img
            src={post.image}
            alt={post.title}
            className="w-full h-full object-cover"
          />
        </div>
      </div>
    </section>
  );
};

export default BlogHeader;
