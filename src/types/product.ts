export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  category: 'fibre' | 'lan' | 'adss-hardware' | 'switches' | 'radios';
  imageUrl: string;
  icon: string;
  specifications?: Record<string, string>;
  inStock: boolean;
  featured?: boolean;
}

export interface CartItem extends Product {
  quantity: number;
}

export interface Category {
  id: string;
  name: string;
  description: string;
  icon: string;
  slug: string;
}