/**
 * Utility functions for consistent product name and description formatting
 */

/**
 * Formats product names with consistent capitalization and spacing
 */
export const formatProductName = (name: string): string => {
  // Convert to title case and clean up formatting
  return name
    .toLowerCase()
    .split(' ')
    .map(word => {
      // Keep certain technical terms in uppercase
      const upperCaseTerms = [
        'cat6', 'cat5e', 'poe', 'sfp', 'gpon', 'adss', 'dna', 'olt', 'cpe', 
        'ss', 'ac', 'lr', 'hd', 'eu', 'af', 'uap', 'uf', 'tc', 'cp', 'wifi',
        'lan', 'wan', 'usb', 'hdmi', 'vga', 'rj45', 'ip', 'tcp', 'udp',
        'http', 'https', 'ftp', 'ssh', 'vpn', 'qos', 'vlan', 'mpls',
        'ospf', 'bgp', 'eigrp', 'rip', 'snmp', 'ntp', 'dhcp', 'dns',
        'led', 'lcd', 'oled', 'ups', 'pdu', 'kvm', 'raid', 'ssd', 'hdd',
        'ram', 'rom', 'cpu', 'gpu', 'api', 'sdk', 'ide', 'gui', 'cli',
        'os', 'vm', 'db', 'sql', 'xml', 'json', 'csv', 'pdf', 'jpg',
        'png', 'gif', 'mp3', 'mp4', 'avi', 'mov', 'zip', 'rar', '7z'
      ];
      const lowerWord = word.toLowerCase();
      
      if (upperCaseTerms.includes(lowerWord)) {
        return word.toUpperCase();
      }
      
      // Handle special cases for model numbers and technical specifications
      if (/^\d+[a-z]*$/i.test(word)) {
        return word.toUpperCase(); // Numbers with letters (like 24P, 4X, etc.)
      }
      
      if (/^[a-z]\d+/i.test(word)) {
        return word.toUpperCase(); // Model numbers starting with letter then number
      }
      
      // Capitalize first letter of each word
      return word.charAt(0).toUpperCase() + word.slice(1);
    })
    .join(' ')
    // Clean up common formatting issues
    .replace(/\s+/g, ' ') // Remove extra spaces
    .replace(/\s*-\s*/g, ' - ') // Standardize dash spacing
    .replace(/\s*\(\s*/g, ' (') // Standardize parentheses spacing
    .replace(/\s*\)\s*/g, ') ')
    .replace(/\s*,\s*/g, ', ') // Standardize comma spacing
    .trim();
};

/**
 * Formats product descriptions with consistent capitalization and punctuation
 */
export const formatProductDescription = (description: string): string => {
  // Ensure description starts with capital letter and ends with period
  const cleaned = description.trim();
  if (!cleaned) return '';
  
  const capitalized = cleaned.charAt(0).toUpperCase() + cleaned.slice(1);
  
  // Add period if it doesn't end with punctuation
  if (!/[.!?]$/.test(capitalized)) {
    return capitalized + '.';
  }
  
  return capitalized;
};

/**
 * Formats technical specifications for consistent display
 */
export const formatSpecification = (key: string, value: string): { key: string; value: string } => {
  const formattedKey = key
    .split(/[\s_-]+/)
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
    
  const formattedValue = value.trim();
  
  return {
    key: formattedKey,
    value: formattedValue
  };
};

/**
 * Truncates product names to a specified length while preserving word boundaries
 */
export const truncateProductName = (name: string, maxLength: number = 50): string => {
  const formatted = formatProductName(name);
  
  if (formatted.length <= maxLength) {
    return formatted;
  }
  
  // Find the last space before the max length
  const truncated = formatted.substring(0, maxLength);
  const lastSpace = truncated.lastIndexOf(' ');
  
  if (lastSpace > 0) {
    return truncated.substring(0, lastSpace) + '...';
  }
  
  return truncated + '...';
};

/**
 * Extracts manufacturer from product name if present
 */
export const extractManufacturer = (name: string): string | null => {
  const commonManufacturers = [
    'cisco', 'ubiquiti', 'trendnet', 'linkbasic', 'schneider', 'teldor', 
    'tru-fix', 'catalyst', 'airfiber', 'unifi'
  ];
  
  const words = name.toLowerCase().split(' ');
  
  for (const manufacturer of commonManufacturers) {
    if (words.some(word => word.includes(manufacturer))) {
      return manufacturer.charAt(0).toUpperCase() + manufacturer.slice(1);
    }
  }
  
  return null;
};
