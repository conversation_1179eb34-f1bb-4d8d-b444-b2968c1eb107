import { <PERSON> } from 'react-router-dom';
import { ArrowRight, Shield, Zap, Users, Star, Usb, Server, HardDrive, Router, Radio } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ProductCard } from '@/components/ProductCard';
import { categories, getFeaturedProducts } from '@/lib/products';
import heroImage from '@/assets/hero-telecom.jpg';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';

const HomePage = () => {
  const featuredProducts = getFeaturedProducts();

  const features = [
    {
      icon: Shield,
      title: 'Reliable Solutions',
      description: 'Enterprise-grade equipment with proven reliability for critical infrastructure.'
    },
    {
      icon: Zap,
      title: 'High Performance',
      description: 'Cutting-edge technology delivering exceptional speed and performance.'
    },
    {
      icon: Users,
      title: 'Expert Support',
      description: '24/7 technical support from certified telecommunications professionals.'
    }
  ];

  const testimonials = [
    {
      name: '<PERSON>',
      company: 'TechCorp Industries',
      rating: 5,
      comment: 'Outstanding fiber optic solutions that transformed our network infrastructure. Highly recommended!',
      imageUrl: '/african-american-business-woman-by-window_1303-10869.jpg'
    },
    {
      name: 'Michael Mwanza',
      company: 'Global Networks Ltd',
      rating: 5,
      comment: 'Professional service and top-quality equipment. Cable Network Solutions exceeded our expectations.',
      imageUrl: '/african-man-black-suit_1157-46903.jpg'
    },
    {
      name: 'John Phiri',
      company: 'Metro Communications',
      rating: 5,
      comment: 'Reliable products and excellent technical support. Our go-to partner for telecom equipment.',
      imageUrl: '/cheerful-young-black-man-manager-600nw-2510693647.jpg'
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative overflow-hidden min-h-[80vh] sm:min-h-[90vh] flex items-center">
        <div className="absolute inset-0">
          <img
            src={heroImage}
            alt="Telecommunications Infrastructure"
            className="w-full h-full object-cover object-center"
          />
          <div className="absolute inset-0 hero-gradient"></div>
        </div>

        <div className="relative container-responsive py-16 sm:py-20 lg:py-24">
          <div className="text-center max-w-5xl mx-auto">
            <Badge className="mb-4 sm:mb-6 animate-fade-in text-xs sm:text-sm">
              Leading Telecommunications Solutions
            </Badge>
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold mb-4 sm:mb-6 animate-fade-in leading-tight">
              Fast. Reliable.{' '}
              <span className="text-gradient font-extrabold tracking-tight">
                Connected.
              </span>
            </h1>
            <p className="text-lg sm:text-xl lg:text-2xl text-muted-foreground mb-6 sm:mb-8 max-w-3xl mx-auto animate-fade-in leading-relaxed">
              Professional telecommunications equipment and solutions for modern network infrastructure.
              Connect your business with industry-leading technology.
            </p>

            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center animate-fade-in max-w-md sm:max-w-none mx-auto">
              <Link to="/products/all" className="w-full sm:w-auto">
                <Button variant="hero" size="lg" className="group w-full sm:w-auto touch-target">
                  Browse Products
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>
              <Link to="/contact" className="w-full sm:w-auto">
                <Button variant="outline" size="lg" className="w-full sm:w-auto touch-target">
                  Contact Us
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Categories */}
      <section className="py-12 sm:py-16 lg:py-24 theme-transition">
        <div className="container-responsive">
          <div className="text-center mb-8 sm:mb-12 lg:mb-16">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold mb-3 sm:mb-4 leading-tight">
              Professional Equipment Categories
            </h2>
            <p className="text-lg sm:text-xl lg:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Discover our comprehensive range of telecommunications equipment designed for modern network infrastructure.
            </p>
          </div>

          <div className="grid-responsive-lg">
            {categories.map((category) => {
              // Map the string identifiers in the product data to meaningful lucide icons
              const iconMap: Record<string, any> = {
                Cable: Usb,           // fibre / cable-related: use USB/cable metaphor
                Network: Server,      // LAN equipment: server/network rack
                HardDrive: HardDrive, // ADSS hardware / enclosures
                Router: Router,       // switches / routers
                Radio: Radio          // radio equipment
              };

              const IconComponent = iconMap[category.icon] ?? Users;

              return (
                <Link key={category.id} to={`/products/${category.slug}`}>
                  <Card className="group hover:card-shadow transition-all duration-300 hover:scale-105 card-gradient">
                    <CardContent className="p-6 text-center">
                      <div className="w-16 h-16 primary-gradient rounded-full flex items-center justify-center mx-auto mb-4 group-hover:animate-pulse-glow">
                        <IconComponent className="h-8 w-8 text-white" aria-hidden="true" />
                      </div>
                      <h3 className="text-xl font-semibold mb-2 group-hover:text-primary transition-colors">
                        {category.name}
                      </h3>
                      <p className="text-muted-foreground">
                        {category.description}
                      </p>
                    </CardContent>
                  </Card>
                </Link>
              );
            })}
          </div>
        </div>
      </section>

      {/* Featured Products */}
      <section className="py-16 lg:py-24 bg-secondary/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4">
              Featured Products
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Our most popular and recommended telecommunications equipment.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredProducts.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-16 lg:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4">
              Why Choose Cable Network Solutions
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              We're committed to providing the highest quality telecommunications equipment and exceptional service.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 primary-gradient rounded-full flex items-center justify-center mx-auto mb-4 animate-float">
                  <feature.icon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-3">{feature.title}</h3>
                <p className="text-muted-foreground">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-16 lg:py-24 bg-secondary/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4">
              What Our Customers Say
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Trusted by telecommunications professionals worldwide.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="card-gradient">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    {[...Array(5)].map((_, i) => {
                      const filled = i < testimonial.rating;
                      const opacity = filled ? ( (i + 1) / 5 ) : 0.25;
                      const colorClass = filled ? 'text-yellow-400' : 'text-muted-foreground';
                      return (
                        <Star key={i} className={`h-4 w-4 ${colorClass}`} style={{ opacity }} />
                      );
                    })}
                  </div>
                  <p className="text-muted-foreground mb-4 italic">
                    "{testimonial.comment}"
                  </p>
                  <div className="flex items-center space-x-3">
                    <Avatar>
                      <AvatarImage src={testimonial.imageUrl} alt={testimonial.name} />
                      <AvatarFallback>{testimonial.name.split(' ').map(n => n[0]).slice(0,2).join('')}</AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-semibold">{testimonial.name}</div>
                      <div className="text-sm text-muted-foreground">{testimonial.company}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;