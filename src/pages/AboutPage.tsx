import { Shield, Target, Eye, Users, Award, Globe } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

const AboutPage = () => {
  const values = [
    {
      icon: Shield,
      title: 'Reliability',
      description: 'We provide enterprise-grade equipment that businesses can depend on for their critical operations.'
    },
    {
      icon: Target,
      title: 'Innovation',
      description: 'Constantly evolving our product offerings to include the latest telecommunications technologies.'
    },
    {
      icon: Users,
      title: 'Customer Focus',
      description: 'Our customers success is our success. We provide personalized service and technical expertise.'
    },
    {
      icon: Award,
      title: 'Quality',
      description: 'Every product we offer meets the highest industry standards for performance and durability.'
    }
  ];

  const stats = [
    { number: '15+', label: 'Years of Experience' },
    { number: '10,000+', label: 'Projects Completed' },
    { number: '500+', label: 'Satisfied Clients' },
    { number: '50+', label: 'Countries Served' }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="py-16 lg:py-24 hero-gradient">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl lg:text-5xl font-bold mb-6">
            About Cable Network Solutions
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            For over 15 years, we've been connecting businesses with cutting-edge telecommunications 
            infrastructure, providing reliable solutions that power modern communication networks.
          </p>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-16 lg:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div>
                <div className="flex items-center mb-4">
                  <Target className="h-8 w-8 text-primary mr-3" />
                  <h2 className="text-2xl font-bold">Our Mission</h2>
                </div>
                <p className="text-muted-foreground leading-relaxed">
                  To provide world-class telecommunications equipment and solutions that enable businesses 
                  to build robust, scalable network infrastructure. We're committed to delivering products 
                  that meet the highest standards of quality, reliability, and performance.
                </p>
              </div>

              <div>
                <div className="flex items-center mb-4">
                  <Eye className="h-8 w-8 text-primary mr-3" />
                  <h2 className="text-2xl font-bold">Our Vision</h2>
                </div>
                <p className="text-muted-foreground leading-relaxed">
                  To be the leading provider of telecommunications solutions globally, driving innovation 
                  and connecting communities through advanced network technologies. We envision a world 
                  where reliable communication infrastructure enables unlimited possibilities.
                </p>
              </div>
            </div>

            <div className="card-gradient rounded-2xl p-8">
              <Globe className="h-16 w-16 text-primary mb-6 mx-auto animate-float" />
              <div className="text-center">
                <h3 className="text-xl font-semibold mb-4">Global Reach</h3>
                <p className="text-muted-foreground">
                  Serving telecommunications projects across 6 continents, from small business networks 
                  to large-scale infrastructure deployments.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats */}
      <section className="py-16 lg:py-24 bg-secondary/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4">
              Proven Track Record
            </h2>
            <p className="text-xl text-muted-foreground">
              Numbers that reflect our commitment to excellence
            </p>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl lg:text-5xl font-bold text-primary mb-2">
                  {stat.number}
                </div>
                <div className="text-muted-foreground font-medium">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Values */}
      <section className="py-16 lg:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4">
              Our Core Values
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              The principles that guide everything we do and every solution we provide
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {values.map((value, index) => (
              <Card key={index} className="card-gradient group hover:card-shadow transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 primary-gradient rounded-lg flex items-center justify-center flex-shrink-0 group-hover:animate-pulse-glow">
                      <value.icon className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold mb-2 group-hover:text-primary transition-colors">
                        {value.title}
                      </h3>
                      <p className="text-muted-foreground leading-relaxed">
                        {value.description}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-16 lg:py-24 bg-secondary/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4">
              Expert Team
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Our telecommunications specialists bring decades of combined experience in network infrastructure 
              and enterprise solutions.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="card-gradient text-center">
              <CardContent className="p-6">
                <div className="w-20 h-20 primary-gradient rounded-full mx-auto mb-4 flex items-center justify-center">
                  <Users className="h-10 w-10 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Engineering Team</h3>
                <p className="text-muted-foreground">
                  Certified network engineers with expertise in fiber optics, wireless systems, and enterprise networking.
                </p>
              </CardContent>
            </Card>

            <Card className="card-gradient text-center">
              <CardContent className="p-6">
                <div className="w-20 h-20 primary-gradient rounded-full mx-auto mb-4 flex items-center justify-center">
                  <Shield className="h-10 w-10 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Quality Assurance</h3>
                <p className="text-muted-foreground">
                  Rigorous testing and quality control processes ensure every product meets industry standards.
                </p>
              </CardContent>
            </Card>

            <Card className="card-gradient text-center">
              <CardContent className="p-6">
                <div className="w-20 h-20 primary-gradient rounded-full mx-auto mb-4 flex items-center justify-center">
                  <Award className="h-10 w-10 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Support Specialists</h3>
                <p className="text-muted-foreground">
                  24/7 technical support from certified professionals who understand telecommunications infrastructure.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  );
};

export default AboutPage;