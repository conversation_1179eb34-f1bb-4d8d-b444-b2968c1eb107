import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON><PERSON><PERSON>, ShoppingBag, Clock, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useCartStore } from '@/store/cartStore';

const CheckoutPage = () => {
  const navigate = useNavigate();
  const { items } = useCartStore();

  return (
    <div className="min-h-screen flex flex-col">
      {/* Header */}
      <div className="px-4 sm:px-6 lg:px-8 py-4">
        <Button
          variant="ghost"
          onClick={() => navigate('/cart')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Cart
        </Button>
      </div>

      {/* Coming Soon Message - Centered Vertically */}
      <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div className="max-w-2xl w-full text-center">
          <div className="card-gradient rounded-lg p-8 elegant-shadow">
            <div className="mb-6 flex flex-col items-center">
              <Badge className="mb-4 bg-primary text-primary-foreground">
                <Sparkles className="h-3 w-3 mr-1" />
                Coming Soon
              </Badge>
              <div className="flex items-center justify-center w-16 h-16 rounded-full primary-gradient">
                <Clock className="h-8 w-8 text-primary-foreground" />
              </div>
            </div>

            <h2 className="text-2xl font-bold mb-4">Online Checkout Coming Soon!</h2>
            <p className="text-muted-foreground text-lg mb-6">
              We're working hard to bring you a seamless online shopping experience.
              Soon you'll be able to purchase our professional telecommunications equipment
              directly through our website with secure payment processing and fast delivery.
            </p>

            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                In the meantime, please contact us directly to place your order:
              </p>

              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button
                  variant="hero"
                  onClick={() => navigate('/contact')}
                  className="group"
                >
                  Contact Us to Order
                  <ArrowLeft className="h-4 w-4 ml-2 rotate-180 group-hover:translate-x-1 transition-transform" />
                </Button>
                <Button
                  variant="outline"
                  onClick={() => navigate('/cart')}
                >
                  <ShoppingBag className="h-4 w-4 mr-2" />
                  Back to Cart
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage;