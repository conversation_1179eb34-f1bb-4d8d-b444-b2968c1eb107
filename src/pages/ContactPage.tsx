import { useState } from 'react';
import { Mail, Phone, MapPin, Clock, Send } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

// Configuration - Change this to your desired email
const CONTACT_EMAIL = '<EMAIL>';

const contactSchema = {
  firstName: (value) => value?.length >= 2 ? null : 'First name must be at least 2 characters',
  lastName: (value) => value?.length >= 2 ? null : 'Last name must be at least 2 characters',
  email: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value) ? null : 'Please enter a valid email address',
  phone: (value) => value?.length >= 10 ? null : 'Please enter a valid phone number',
  subject: (value) => value?.length >= 5 ? null : 'Subject must be at least 5 characters',
  category: (value) => value ? null : 'Please select a category',
  message: (value) => value?.length >= 10 ? null : 'Message must be at least 10 characters',
};

const ContactPage = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showToast, setShowToast] = useState(null);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    company: '',
    subject: '',
    category: '',
    message: '',
  });
  const [errors, setErrors] = useState({});

  const validateForm = () => {
    const newErrors = {};
    Object.keys(contactSchema).forEach(field => {
      const error = contactSchema[field](formData[field]);
      if (error) newErrors[field] = error;
    });
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  const showToastMessage = (title, description, variant = 'default') => {
    setShowToast({ title, description, variant });
    setTimeout(() => setShowToast(null), 5000);
  };

  const onSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    setIsSubmitting(true);
    
    try {
      // Create email body
      const emailBody = `
New Contact Form Submission

Name: ${formData.firstName} ${formData.lastName}
Email: ${formData.email}
Phone: ${formData.phone}
Company: ${formData.company || 'Not specified'}
Category: ${formData.category}
Subject: ${formData.subject}

Message:
${formData.message}

---
Sent from Cable Network Contact Form
      `.trim();

      // Create mailto link
      const mailtoLink = `mailto:${CONTACT_EMAIL}?subject=${encodeURIComponent(`Contact Form: ${formData.subject}`)}&body=${encodeURIComponent(emailBody)}`;
      
      // Open email client
      window.location.href = mailtoLink;
      
      showToastMessage(
        "Email client opened!",
        "Your default email client should now be open with the message ready to send."
      );
      
      // Reset form after short delay
      setTimeout(() => {
        setFormData({
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
          company: '',
          subject: '',
          category: '',
          message: '',
        });
      }, 1000);
      
    } catch (error) {
      showToastMessage(
        "Failed to open email client",
        "Please try again or contact us directly at " + CONTACT_EMAIL,
        "destructive"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const contactInfo = [
    {
      icon: Mail,
      label: 'Email',
      value: '<EMAIL>',
      description: 'Quick response guaranteed'
    },
    {
      icon: Phone,
      label: 'Phone Support',
      value: '(+260) **********',
      description: 'Available 24/7'
    },
    {
      icon: MapPin,
      label: 'Office Location',
      value: 'Garden View Offices, Block B6 North, Lilayi, Lusaka',
      description: 'Visit our headquarters'
    },
    {
      icon: Clock,
      label: 'Follow Us',
      value: 'Socials',
      description: 'Stay connected with us',
      socialLinks: [
        {
          name: 'Facebook',
          url: 'https://www.facebook.com/share/1763CXaYgi/?mibextid=wwXIfr',
          icon: (
            <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
            </svg>
          )
        },
        {
          name: 'Instagram',
          url: 'https://www.instagram.com/cablenetworksolutions?igsh=NXpubWY1bDh0YzJ4',
          icon: (
            <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path fillRule="evenodd" d="M12.017 0C8.396 0 7.989.013 7.041.048 6.094.082 5.52.204 5.012.388a6.5 6.5 0 00-2.346 1.267A6.5 6.5 0 00.388 5.012C.204 5.52.082 6.094.048 7.041.013 7.989 0 8.396 0 12.017c0 3.624.013 4.09.048 5.016.034.947.156 1.521.34 2.029a6.5 6.5 0 001.267 2.346 6.5 6.5 0 002.346 1.267c.508.184 1.082.306 2.029.34.926.035 1.392.048 5.016.048 3.624 0 4.09-.013 5.016-.048.947-.034 1.521-.156 2.029-.34a6.5 6.5 0 002.346-1.267 6.5 6.5 0 001.267-2.346c.184-.508.306-1.082.34-2.029.035-.926.048-1.392.048-5.016 0-3.624-.013-4.09-.048-5.016-.034-.947-.156-1.521-.34-2.029a6.5 6.5 0 00-1.267-2.346A6.5 6.5 0 0019.029.388c-.508-.184-1.082-.306-2.029-.34C16.074.013 15.668 0 12.017 0zM12.017 2.13c3.568 0 3.993.014 5.404.048.823.034 1.269.156 1.565.26.393.152.674.334.969.63.295.295.478.576.63.969.104.296.226.742.26 1.565.034 1.411.048 1.836.048 5.404 0 3.568-.014 3.993-.048 5.404-.034.823-.156 1.269-.26 1.565a2.607 2.607 0 01-.63.969 2.607 2.607 0 01-.969.63c-.296.104-.742.226-1.565.26-1.411.034-1.836.048-5.404.048-3.568 0-3.993-.014-5.404-.048-.823-.034-1.269-.156-1.565-.26a2.607 2.607 0 01-.969-.63 2.607 2.607 0 01-.63-.969c-.104-.296-.226-.742-.26-1.565-.034-1.411-.048-1.836-.048-5.404 0-3.568.014-3.993.048-5.404.034-.823.156-1.269.26-1.565.152-.393.334-.674.63-.969.295-.295.576-.478.969-.63.296-.104.742-.226 1.565-.26 1.411-.034 1.836-.048 5.404-.048z" clipRule="evenodd" />
              <path fillRule="evenodd" d="M12.017 5.838a6.179 6.179 0 100 12.358 6.179 6.179 0 000-12.358zM12.017 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z" clipRule="evenodd" />
            </svg>
          )
        },
        {
          name: 'LinkedIn',
          url: 'https://www.linkedin.com/company/cable-network-solutionszm/',
          icon: (
            <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path fillRule="evenodd" d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" clipRule="evenodd" />
            </svg>
          )
        }
      ]
    }
  ];

  return (
    <div className="min-h-screen py-8 sm:py-12 lg:py-16 theme-transition">
      <div className="container-responsive">
        {/* Toast Notification */}
        {showToast && (
          <div className={`fixed top-4 right-4 z-50 p-4 rounded-xl shadow-2xl max-w-md backdrop-blur-sm border transition-all duration-300 ${
            showToast.variant === 'destructive'
              ? 'bg-destructive/10 border-destructive/20 text-destructive dark:bg-destructive/20'
              : 'bg-primary/10 border-primary/20 text-primary dark:bg-primary/20'
          }`}>
            <h4 className="font-semibold mb-1">{showToast.title}</h4>
            <p className="text-sm opacity-90">{showToast.description}</p>
          </div>
        )}

        {/* Header */}
        <div className="text-center mb-8 sm:mb-12 lg:mb-16">
          <Badge className="mb-4 sm:mb-6 animate-fade-in">
            Professional Telecommunications Support
          </Badge>
          <h1 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold mb-4 sm:mb-6 leading-tight">
            Get in{' '}
            <span className="bg-gradient-to-r from-primary to-primary-glow bg-clip-text text-transparent">
              Touch
            </span>
          </h1>
          <p className="text-lg sm:text-xl lg:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Have questions about our telecommunications solutions? Our expert team is here to help
            you find the right equipment for your network infrastructure needs.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
          {/* Contact Information */}
          <div className="space-y-6">
            <Card className="card-gradient border-border/50 shadow-xl hover:shadow-2xl transition-all duration-300 theme-transition">
              <CardContent className="p-6">
                <h2 className="text-xl font-bold mb-6 text-primary">
                  Contact Information
                </h2>
                <div className="space-y-6">
                  {contactInfo.slice(0, 3).map((info, index) => (
                    <div key={index} className="flex items-start space-x-4 group">
                      <div className="w-12 h-12 primary-gradient rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg group-hover:scale-110 transition-transform duration-200">
                        <info.icon className="h-5 w-5 text-primary-foreground" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-foreground mb-1">
                          {info.label}
                        </h3>
                        <p className="text-primary font-medium mb-1">
                          {info.value}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {info.description}
                        </p>
                      </div>
                    </div>
                  ))}

                  {/* Dedicated Social Media Section */}
                  <div className="pt-4 border-t border-border/50">
                    <div className="text-center space-y-4">
                      <h3 className="font-semibold text-foreground text-lg">
                        Stay Connected
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        Follow us for updates and industry insights
                      </p>
                      <div className="flex justify-center space-x-4">
                        {contactInfo[3].socialLinks.map((social, socialIndex) => (
                          <a
                            key={socialIndex}
                            href={social.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="group relative"
                            title={social.name}
                          >
                            <div className="w-12 h-12 bg-muted hover:bg-primary rounded-xl flex items-center justify-center transition-all duration-300 group-hover:scale-110 group-hover:shadow-lg">
                              <div className="text-muted-foreground group-hover:text-primary-foreground transition-colors">
                                {social.icon}
                              </div>
                            </div>
                            <span className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-xs text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                              {social.name}
                            </span>
                          </a>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="card-gradient border-border/50 shadow-xl hover:shadow-2xl transition-all duration-300 theme-transition">
              <CardContent className="p-6">
                <h2 className="text-xl font-bold mb-6 text-primary">
                  Why Choose Us?
                </h2>
                <ul className="space-y-4 text-sm">
                  {[
                    '15+ years of telecommunications expertise',
                    'Enterprise-grade equipment and solutions',
                    '24/7 technical support and consultation',
                    'Competitive pricing and bulk discounts',
                    'Nationwide shipping and installation services'
                  ].map((item, index) => (
                    <li key={index} className="flex items-center space-x-3 group">
                      <div className="w-2 h-2 bg-primary rounded-full group-hover:scale-150 transition-transform duration-200"></div>
                      <span className="text-foreground">{item}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* Contact Form */}
          <div className="lg:col-span-2">
            <Card className="card-gradient border-border/50 shadow-xl hover:shadow-2xl transition-all duration-300 theme-transition">
              <CardContent className="p-6 sm:p-8">
                <h2 className="text-2xl font-bold mb-6 sm:mb-8 text-primary">
                  Send us a Message
                </h2>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-foreground">First Name</label>
                    <input
                      type="text"
                      value={formData.firstName}
                      onChange={(e) => handleInputChange('firstName', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200 bg-background theme-transition ${
                        errors.firstName ? 'border-destructive ring-2 ring-destructive/20' : 'border-input hover:border-primary'
                      }`}
                      placeholder="Enter your first name"
                    />
                    {errors.firstName && <p className="text-destructive text-sm mt-1">{errors.firstName}</p>}
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-foreground">Last Name</label>
                    <input
                      type="text"
                      value={formData.lastName}
                      onChange={(e) => handleInputChange('lastName', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200 bg-background theme-transition ${
                        errors.lastName ? 'border-destructive ring-2 ring-destructive/20' : 'border-input hover:border-primary'
                      }`}
                      placeholder="Enter your last name"
                    />
                    {errors.lastName && <p className="text-destructive text-sm mt-1">{errors.lastName}</p>}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-foreground">Email Address</label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200 bg-background theme-transition ${
                        errors.email ? 'border-destructive ring-2 ring-destructive/20' : 'border-input hover:border-primary'
                      }`}
                      placeholder="<EMAIL>"
                    />
                    {errors.email && <p className="text-destructive text-sm mt-1">{errors.email}</p>}
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-foreground">Phone Number</label>
                    <input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200 bg-background theme-transition ${
                        errors.phone ? 'border-destructive ring-2 ring-destructive/20' : 'border-input hover:border-primary'
                      }`}
                      placeholder="+260 **********"
                    />
                    {errors.phone && <p className="text-destructive text-sm mt-1">{errors.phone}</p>}
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-semibold text-foreground">Company (Optional)</label>
                  <input
                    type="text"
                    value={formData.company}
                    onChange={(e) => handleInputChange('company', e.target.value)}
                    className="w-full px-4 py-3 border border-input rounded-xl focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200 bg-background theme-transition hover:border-primary"
                    placeholder="Your company name"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-foreground">Category</label>
                    <select
                      value={formData.category}
                      onChange={(e) => handleInputChange('category', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200 bg-background theme-transition ${
                        errors.category ? 'border-destructive ring-2 ring-destructive/20' : 'border-input hover:border-primary'
                      }`}
                    >
                      <option value="">Select a category</option>
                      <option value="general">General Inquiry</option>
                      <option value="sales">Sales Question</option>
                      <option value="technical">Technical Support</option>
                      <option value="quote">Request Quote</option>
                      <option value="partnership">Partnership</option>
                    </select>
                    {errors.category && <p className="text-destructive text-sm mt-1">{errors.category}</p>}
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-foreground">Subject</label>
                    <input
                      type="text"
                      value={formData.subject}
                      onChange={(e) => handleInputChange('subject', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200 bg-background theme-transition ${
                        errors.subject ? 'border-destructive ring-2 ring-destructive/20' : 'border-input hover:border-primary'
                      }`}
                      placeholder="Brief description of your inquiry"
                    />
                    {errors.subject && <p className="text-destructive text-sm mt-1">{errors.subject}</p>}
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-semibold text-foreground">Message</label>
                  <textarea
                    rows={6}
                    value={formData.message}
                    onChange={(e) => handleInputChange('message', e.target.value)}
                    placeholder="Tell us about your telecommunications needs in detail..."
                    className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent resize-vertical transition-all duration-200 bg-background theme-transition ${
                      errors.message ? 'border-destructive ring-2 ring-destructive/20' : 'border-input hover:border-primary'
                    }`}
                  />
                  {errors.message && <p className="text-destructive text-sm mt-1">{errors.message}</p>}
                </div>

                <Button
                  onClick={onSubmit}
                  className="w-full py-4 px-8 font-semibold shadow-lg hover:shadow-xl hover:scale-105 active:scale-95 touch-target"
                  disabled={isSubmitting}
                  size="lg"
                >
                  {isSubmitting ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-2 border-primary-foreground border-t-transparent mr-3"></div>
                      Processing...
                    </div>
                  ) : (
                    <>
                      <Send className="h-5 w-5 mr-2" />
                      Send Message
                    </>
                  )}
                </Button>

                <p className="text-sm text-muted-foreground text-center mt-4">
                  By submitting this form, your default email client will open with a pre-filled message ready to send.
                </p>
              </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactPage;