import { use<PERSON><PERSON><PERSON>, Link, Navigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { getPostById } from '@/lib/blogData';
import BlogContent from '@/components/BlogContent';
import BlogHeader from '@/components/BlogHeader';

const BlogPostPage = () => {
  const { id } = useParams<{ id: string }>();
  const post = id ? getPostById(id) : undefined;


  if (!post) {
    return <Navigate to="/news" replace />;
  }



  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: post.title,
          text: post.excerpt,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      // You could show a toast notification here
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-secondary/10">
      {/* Back Navigation */}
      <section className="py-4 border-b border-border/50 backdrop-blur-sm bg-background/80 sticky top-0 z-10">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <Link to="/news">
            <Button variant="ghost" className="group hover:bg-secondary/50">
              <ArrowLeft className="h-4 w-4 mr-2 group-hover:-translate-x-1 transition-transform" />
              Back to Article List
            </Button>
          </Link>
        </div>
      </section>

      <BlogHeader post={post} onShare={handleShare} />

      {/* Article Content */}
      <section className="pt-2 pb-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-card/50 backdrop-blur-sm rounded-3xl p-8 lg:p-12 shadow-xl ring-1 ring-border/50">
            <BlogContent content={post.content} tags={post.tags} />
          </div>
        </div>
      </section>
    </div>
  );
};

export default BlogPostPage;
